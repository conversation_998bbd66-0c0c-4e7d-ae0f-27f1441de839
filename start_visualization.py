#!/usr/bin/env python3
"""
RocketSimVis Visualization Starter
This script starts the RocketSimVis visualization tool for monitoring training.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_requirements():
    """Check if RocketSimVis requirements are met."""
    
    vis_dir = Path("RocketSimVis")
    if not vis_dir.exists():
        print("❌ RocketSimVis directory not found")
        print("Please run setup_environment.py first")
        return False
    
    requirements_file = vis_dir / "requirements.txt"
    if not requirements_file.exists():
        print("❌ RocketSimVis requirements.txt not found")
        return False
    
    # Check if main.py exists
    main_py = vis_dir / "src" / "main.py"
    if not main_py.exists():
        print("❌ RocketSimVis main.py not found")
        return False
    
    return True

def install_requirements():
    """Install RocketSimVis requirements."""
    print("Installing RocketSimVis requirements...")
    
    vis_dir = Path("RocketSimVis")
    requirements_file = vis_dir / "requirements.txt"
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True, capture_output=True, text=True)
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False

def start_visualization():
    """Start the RocketSimVis visualization."""
    print("Starting RocketSimVis...")
    print("=" * 40)
    
    vis_dir = Path("RocketSimVis")
    
    # Change to RocketSimVis directory and run
    try:
        # Use the RUN.bat if on Windows, otherwise run main.py directly
        if os.name == 'nt' and (vis_dir / "RUN.bat").exists():
            print("Using RUN.bat...")
            result = subprocess.run(["RUN.bat"], cwd=vis_dir, shell=True)
        else:
            print("Running main.py directly...")
            result = subprocess.run([sys.executable, "src/main.py"], cwd=vis_dir)
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n\nVisualization stopped by user")
        return 0
    except Exception as e:
        print(f"❌ Error starting visualization: {e}")
        return 1

def show_usage_info():
    """Show information about using the visualization."""
    print("\nRocketSimVis Usage Information:")
    print("=" * 40)
    print("• The visualization will start and wait for training data")
    print("• Start your training with start_training.py to see the bots in action")
    print("• Use mouse to rotate camera, scroll to zoom")
    print("• Press number keys to switch between player cameras")
    print("• Press ESC to exit")
    print("\nNetwork Settings:")
    print("• Default port: 7777")
    print("• Protocol: UDP")
    print("• The training script will automatically send data to the visualizer")

def main():
    print("RocketSimVis Visualization Starter")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements not met.")
        return 1
    
    # Install requirements if needed
    print("Checking Python requirements...")
    if not install_requirements():
        return 1
    
    # Show usage information
    show_usage_info()
    
    # Ask user if they want to continue
    print("\n" + "=" * 40)
    response = input("Start RocketSimVis? (y/n): ").lower().strip()
    
    if response not in ['y', 'yes']:
        print("Cancelled by user")
        return 0
    
    # Start visualization
    return start_visualization()

if __name__ == "__main__":
    sys.exit(main())
