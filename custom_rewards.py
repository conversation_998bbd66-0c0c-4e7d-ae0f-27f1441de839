"""
Custom Reward Functions for RLGymPPO_CPP
This module contains various reward functions optimized for different training scenarios.
"""

import numpy as np
from typing import Dict, Any, List
import math

class CustomRewardFunction:
    """Base class for custom reward functions."""
    
    def __init__(self, weight: float = 1.0):
        self.weight = weight
    
    def reset(self, initial_state):
        """Reset any internal state."""
        pass
    
    def get_reward(self, player, state, previous_action) -> float:
        """Calculate reward for a player."""
        raise NotImplementedError

class GoalReward(CustomRewardFunction):
    """Reward for scoring goals."""
    
    def __init__(self, weight: float = 100.0):
        super().__init__(weight)
        self.last_goals = {}
    
    def reset(self, initial_state):
        self.last_goals = {i: 0 for i in range(len(initial_state.players))}
    
    def get_reward(self, player, state, previous_action) -> float:
        player_id = player.car_id
        current_goals = player.match_goals
        
        if player_id not in self.last_goals:
            self.last_goals[player_id] = current_goals
            return 0.0
        
        goal_diff = current_goals - self.last_goals[player_id]
        self.last_goals[player_id] = current_goals
        
        return goal_diff * self.weight

class BallTouchReward(CustomRewardFunction):
    """Reward for touching the ball."""
    
    def __init__(self, weight: float = 1.0):
        super().__init__(weight)
        self.last_ball_touch = {}
    
    def reset(self, initial_state):
        self.last_ball_touch = {i: False for i in range(len(initial_state.players))}
    
    def get_reward(self, player, state, previous_action) -> float:
        player_id = player.car_id
        current_touch = player.ball_touched
        
        if player_id not in self.last_ball_touch:
            self.last_ball_touch[player_id] = current_touch
            return 0.0
        
        if current_touch and not self.last_ball_touch[player_id]:
            reward = self.weight
        else:
            reward = 0.0
        
        self.last_ball_touch[player_id] = current_touch
        return reward

class VelocityReward(CustomRewardFunction):
    """Reward for maintaining good velocity toward objectives."""
    
    def __init__(self, weight: float = 0.1):
        super().__init__(weight)
    
    def get_reward(self, player, state, previous_action) -> float:
        # Reward velocity toward ball
        ball_pos = np.array([state.ball.position.x, state.ball.position.y, state.ball.position.z])
        car_pos = np.array([player.car_data.position.x, player.car_data.position.y, player.car_data.position.z])
        car_vel = np.array([player.car_data.velocity.x, player.car_data.velocity.y, player.car_data.velocity.z])
        
        to_ball = ball_pos - car_pos
        to_ball_norm = np.linalg.norm(to_ball)
        
        if to_ball_norm > 0:
            to_ball_unit = to_ball / to_ball_norm
            vel_toward_ball = np.dot(car_vel, to_ball_unit)
            return vel_toward_ball * self.weight
        
        return 0.0

class SaveReward(CustomRewardFunction):
    """Reward for defensive saves."""
    
    def __init__(self, weight: float = 50.0):
        super().__init__(weight)
        self.last_ball_vel = None
    
    def reset(self, initial_state):
        self.last_ball_vel = None
    
    def get_reward(self, player, state, previous_action) -> float:
        # Detect if ball was heading toward goal and player changed its direction
        ball_pos = np.array([state.ball.position.x, state.ball.position.y, state.ball.position.z])
        ball_vel = np.array([state.ball.velocity.x, state.ball.velocity.y, state.ball.velocity.z])
        
        # Determine which goal to defend based on team
        if player.team_num == 0:  # Blue team defends negative Y goal
            goal_pos = np.array([0, -5120, 0])
        else:  # Orange team defends positive Y goal
            goal_pos = np.array([0, 5120, 0])
        
        reward = 0.0
        
        if self.last_ball_vel is not None and player.ball_touched:
            # Check if ball was heading toward goal
            to_goal = goal_pos - ball_pos
            was_heading_to_goal = np.dot(self.last_ball_vel, to_goal) > 0
            
            # Check if ball direction changed away from goal
            now_heading_to_goal = np.dot(ball_vel, to_goal) > 0
            
            if was_heading_to_goal and not now_heading_to_goal:
                reward = self.weight
        
        self.last_ball_vel = ball_vel.copy()
        return reward

class AerialReward(CustomRewardFunction):
    """Reward for aerial plays."""
    
    def __init__(self, weight: float = 2.0, min_height: float = 300.0):
        super().__init__(weight)
        self.min_height = min_height
    
    def get_reward(self, player, state, previous_action) -> float:
        car_height = player.car_data.position.z
        ball_height = state.ball.position.z
        
        # Reward for being airborne and near the ball
        if car_height > self.min_height and player.ball_touched:
            height_factor = min(car_height / 1000.0, 2.0)  # Cap at 2x
            return self.weight * height_factor
        
        return 0.0

class BoostManagementReward(CustomRewardFunction):
    """Reward for good boost management."""
    
    def __init__(self, weight: float = 0.1):
        super().__init__(weight)
        self.last_boost = {}
    
    def reset(self, initial_state):
        self.last_boost = {i: 100.0 for i in range(len(initial_state.players))}
    
    def get_reward(self, player, state, previous_action) -> float:
        player_id = player.car_id
        current_boost = player.boost_amount
        
        if player_id not in self.last_boost:
            self.last_boost[player_id] = current_boost
            return 0.0
        
        # Small penalty for wasting boost when not needed
        boost_used = self.last_boost[player_id] - current_boost
        
        # Reward efficient boost usage (touching ball while using boost)
        if boost_used > 0 and player.ball_touched:
            reward = self.weight
        elif boost_used > 5:  # Penalize excessive boost usage
            reward = -self.weight * 0.1
        else:
            reward = 0.0
        
        self.last_boost[player_id] = current_boost
        return reward

class CombinedRewardFunction:
    """Combines multiple reward functions with weights."""
    
    def __init__(self):
        self.rewards = [
            GoalReward(weight=100.0),
            BallTouchReward(weight=1.0),
            VelocityReward(weight=0.1),
            SaveReward(weight=50.0),
            AerialReward(weight=2.0),
            BoostManagementReward(weight=0.1),
        ]
    
    def reset(self, initial_state):
        for reward in self.rewards:
            reward.reset(initial_state)
    
    def get_reward(self, player, state, previous_action) -> float:
        total_reward = 0.0
        for reward in self.rewards:
            total_reward += reward.get_reward(player, state, previous_action)
        return total_reward

# Preset configurations
AGGRESSIVE_REWARDS = CombinedRewardFunction()
AGGRESSIVE_REWARDS.rewards = [
    GoalReward(weight=150.0),
    BallTouchReward(weight=2.0),
    VelocityReward(weight=0.2),
    AerialReward(weight=3.0),
]

DEFENSIVE_REWARDS = CombinedRewardFunction()
DEFENSIVE_REWARDS.rewards = [
    GoalReward(weight=100.0),
    SaveReward(weight=75.0),
    BallTouchReward(weight=0.5),
    VelocityReward(weight=0.1),
    BoostManagementReward(weight=0.2),
]

BALANCED_REWARDS = CombinedRewardFunction()  # Uses default weights
