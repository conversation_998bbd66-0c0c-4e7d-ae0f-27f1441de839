#!/usr/bin/env python3
"""
RLGymPPO_CPP Build Script
This script builds the C++ project with proper DirectML support.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
import urllib.request
import zipfile

def run_command(cmd, cwd=None, check=True):
    """Run a command and return the result."""
    print(f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    if isinstance(cmd, str):
        cmd = cmd.split()
    
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error running command: {cmd}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        return False
    
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    
    return result.returncode == 0

def check_cmake():
    """Check if CMake is available."""
    try:
        result = subprocess.run(["cmake", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ CMake found: {result.stdout.split()[2]}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ CMake not found. Please install CMake.")
    print("Download from: https://cmake.org/download/")
    return False

def check_visual_studio():
    """Check if Visual Studio is available (Windows only)."""
    if platform.system() != "Windows":
        return True
    
    # Check for Visual Studio Build Tools or Visual Studio
    vs_paths = [
        "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools",
        "C:/Program Files (x86)/Microsoft Visual Studio/2022/Community",
        "C:/Program Files (x86)/Microsoft Visual Studio/2022/Professional",
        "C:/Program Files (x86)/Microsoft Visual Studio/2022/Enterprise",
        "C:/Program Files/Microsoft Visual Studio/2022/BuildTools",
        "C:/Program Files/Microsoft Visual Studio/2022/Community",
        "C:/Program Files/Microsoft Visual Studio/2022/Professional",
        "C:/Program Files/Microsoft Visual Studio/2022/Enterprise",
    ]
    
    for path in vs_paths:
        if Path(path).exists():
            print(f"✓ Visual Studio found at: {path}")
            return True
    
    print("❌ Visual Studio not found.")
    print("Please install Visual Studio 2022 with C++ Desktop Development workload")
    print("Download from: https://visualstudio.microsoft.com/downloads/")
    return False

def download_libtorch():
    """Download LibTorch if not present."""
    libtorch_dir = Path("RLGymPPO_CPP/RLGymPPO_CPP/libtorch")
    
    if libtorch_dir.exists():
        print("✓ LibTorch already exists")
        return True
    
    print("Downloading LibTorch...")
    
    # Use CPU version for DirectML compatibility
    if platform.system() == "Windows":
        url = "https://download.pytorch.org/libtorch/cpu/libtorch-win-shared-with-deps-2.1.0%2Bcpu.zip"
    else:
        url = "https://download.pytorch.org/libtorch/cpu/libtorch-cxx11-abi-shared-with-deps-2.1.0%2Bcpu.zip"
    
    zip_file = "libtorch.zip"
    
    try:
        print(f"Downloading from: {url}")
        urllib.request.urlretrieve(url, zip_file)
        
        print("Extracting LibTorch...")
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall("RLGymPPO_CPP/RLGymPPO_CPP/")
        
        os.remove(zip_file)
        print("✓ LibTorch downloaded and extracted")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download LibTorch: {e}")
        return False

def setup_collision_meshes():
    """Setup collision meshes directory."""
    collision_dir = Path("collision_meshes")
    if not collision_dir.exists():
        collision_dir.mkdir()
        print("Created collision_meshes directory")
        
        # Create a note file
        note_file = collision_dir / "README.txt"
        with open(note_file, 'w') as f:
            f.write("Place your collision_meshes files here.\n")
            f.write("These files are required for RocketSim to work properly.\n")
            f.write("You can get them from the RocketSim repository or RLGym-PPO setup.\n")
        
        print("⚠ Please add collision mesh files to the collision_meshes/ directory")
        return False
    else:
        # Check if directory has files
        files = list(collision_dir.glob("*"))
        if not files or all(f.name.startswith("README") for f in files):
            print("⚠ collision_meshes directory exists but appears empty")
            return False
        else:
            print("✓ collision_meshes directory found with files")
            return True

def configure_cmake():
    """Configure the CMake project."""
    print("\n=== Configuring CMake ===")
    
    build_dir = Path("RLGymPPO_CPP/build")
    build_dir.mkdir(exist_ok=True)
    
    # CMake configuration command
    cmake_cmd = [
        "cmake",
        "..",
        "-DCMAKE_BUILD_TYPE=RelWithDebInfo",
    ]
    
    # Add Visual Studio generator on Windows
    if platform.system() == "Windows":
        cmake_cmd.extend(["-G", "Visual Studio 17 2022", "-A", "x64"])
    
    return run_command(cmake_cmd, cwd=build_dir)

def build_project():
    """Build the project."""
    print("\n=== Building Project ===")
    
    build_dir = Path("RLGymPPO_CPP/build")
    
    # Build command
    cmake_cmd = [
        "cmake",
        "--build", ".",
        "--config", "RelWithDebInfo",
        "--parallel"
    ]
    
    return run_command(cmake_cmd, cwd=build_dir)

def copy_executable():
    """Copy the built executable to the root directory."""
    print("\n=== Copying Executable ===")
    
    # Find the executable
    build_dir = Path("RLGymPPO_CPP/build")
    
    if platform.system() == "Windows":
        exe_patterns = [
            "RelWithDebInfo/RLGymPPO_CPP_Example.exe",
            "Debug/RLGymPPO_CPP_Example.exe",
            "Release/RLGymPPO_CPP_Example.exe",
            "RLGymPPO_CPP_Example.exe"
        ]
    else:
        exe_patterns = [
            "RLGymPPO_CPP_Example",
        ]
    
    exe_path = None
    for pattern in exe_patterns:
        candidate = build_dir / pattern
        if candidate.exists():
            exe_path = candidate
            break
    
    if not exe_path:
        print("❌ Could not find built executable")
        return False
    
    # Copy to root
    dest_path = Path("RLGymPPO_CPP_Training.exe" if platform.system() == "Windows" else "RLGymPPO_CPP_Training")
    
    try:
        shutil.copy2(exe_path, dest_path)
        print(f"✓ Executable copied to: {dest_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to copy executable: {e}")
        return False

def main():
    print("RLGymPPO_CPP Build Script")
    print("=" * 40)
    
    # Check requirements
    if not check_cmake():
        return 1
    
    if not check_visual_studio():
        return 1
    
    # Download LibTorch if needed
    if not download_libtorch():
        return 1
    
    # Setup collision meshes
    collision_ok = setup_collision_meshes()
    
    # Configure CMake
    if not configure_cmake():
        print("❌ CMake configuration failed")
        return 1
    
    # Build project
    if not build_project():
        print("❌ Build failed")
        return 1
    
    # Copy executable
    if not copy_executable():
        print("❌ Failed to copy executable")
        return 1
    
    print("\n" + "=" * 40)
    print("✓ Build completed successfully!")
    
    if not collision_ok:
        print("\n⚠ Warning: collision_meshes directory needs to be populated")
        print("Please add collision mesh files before running training")
    
    print("\nNext steps:")
    print("1. Run setup_environment.py to install Python dependencies")
    print("2. Add collision mesh files to collision_meshes/ directory")
    print("3. Run start_training.py to begin training")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
