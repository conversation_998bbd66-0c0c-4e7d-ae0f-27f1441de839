@echo off
title RLGymPPO_CPP DirectML Complete Setup

echo ========================================
echo RLGymPPO_CPP DirectML Complete Setup
echo ========================================
echo.
echo This script will set up everything you need for training:
echo 1. Install Python dependencies (DirectML, etc.)
echo 2. Download and configure LibTorch
echo 3. Build the C++ project
echo 4. Create configuration templates
echo 5. Setup directory structure
echo.
echo Prerequisites:
echo - Python 3.8+ installed
echo - Visual Studio 2022 with C++ Desktop Development
echo - CMake 3.8+
echo - Git
echo.
echo Make sure you have these installed before continuing!
echo.
pause

echo.
echo ========================================
echo Step 1: Setting up Python environment
echo ========================================
echo.
python setup_environment.py
if errorlevel 1 (
    echo.
    echo ❌ Environment setup failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 2: Creating configuration templates
echo ========================================
echo.
python config_templates.py
if errorlevel 1 (
    echo.
    echo ❌ Config template creation failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 3: Building C++ project
echo ========================================
echo.
python build_project.py
if errorlevel 1 (
    echo.
    echo ❌ Build failed!
    echo Please check that you have Visual Studio 2022 installed.
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✓ Setup Complete!
echo ========================================
echo.
echo Your RLGymPPO_CPP DirectML environment is ready!
echo.
echo IMPORTANT: Before training, you need to:
echo 1. Add collision mesh files to the collision_meshes/ directory
echo    (Download from RocketSim or RLGym-PPO repositories)
echo.
echo Quick Start:
echo 1. Add collision meshes to collision_meshes/
echo 2. Run 3_Start_Training.bat to begin training
echo 3. Run 4_Start_Visualization.bat to watch your bots train
echo.
echo Available configurations:
echo - default: Balanced training
echo - fast: Quick testing
echo - high_performance: For powerful systems
echo - 1v1, 2v2, 3v3: Team-specific training
echo - aerial: Aerial skill focus
echo.
echo Available reward types:
echo - balanced: Well-rounded gameplay
echo - aggressive: Offensive focus
echo - defensive: Defensive focus
echo.
echo Example usage:
echo python start_training.py --config 1v1 --reward aggressive
echo.
echo ========================================
echo Happy training! 🚀
echo ========================================
pause
