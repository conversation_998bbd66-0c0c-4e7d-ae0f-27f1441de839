#!/usr/bin/env python3
"""
Setup Checker for RLGymPPO_CPP DirectML
This script checks if your environment is properly set up for training.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python():
    """Check Python version and installation."""
    print("🐍 Checking Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required, found:", sys.version)
        return False
    
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_packages():
    """Check required Python packages."""
    print("\n📦 Checking Python packages...")
    
    required_packages = [
        "torch",
        "torch_directml",
        "numpy",
        "wandb",
        "matplotlib",
        "opencv-python",
        "psutil"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Run: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_directml():
    """Check DirectML availability."""
    print("\n🎮 Checking DirectML...")
    
    try:
        import torch_directml
        if torch_directml.is_available():
            device = torch_directml.device()
            print(f"✓ DirectML available: {device}")
            return True
        else:
            print("⚠ DirectML installed but not available")
            return False
    except ImportError:
        print("❌ DirectML not installed")
        return False

def check_cmake():
    """Check CMake installation."""
    print("\n🔨 Checking CMake...")
    
    try:
        result = subprocess.run(["cmake", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.split()[2]
            print(f"✓ CMake {version}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ CMake not found")
    print("Download from: https://cmake.org/download/")
    return False

def check_visual_studio():
    """Check Visual Studio (Windows only)."""
    if platform.system() != "Windows":
        return True
        
    print("\n🏗️ Checking Visual Studio...")
    
    vs_paths = [
        "C:/Program Files (x86)/Microsoft Visual Studio/2022",
        "C:/Program Files/Microsoft Visual Studio/2022",
    ]
    
    for base_path in vs_paths:
        for edition in ["BuildTools", "Community", "Professional", "Enterprise"]:
            path = Path(base_path) / edition
            if path.exists():
                print(f"✓ Visual Studio 2022 {edition}")
                return True
    
    print("❌ Visual Studio 2022 not found")
    print("Install from: https://visualstudio.microsoft.com/downloads/")
    return False

def check_project_structure():
    """Check project directory structure."""
    print("\n📁 Checking project structure...")
    
    required_dirs = [
        "RLGymPPO_CPP",
        "RocketSimVis",
    ]
    
    optional_dirs = [
        "collision_meshes",
        "configs",
        "models",
        "logs"
    ]
    
    all_good = True
    
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✓ {dir_name}/")
        else:
            print(f"❌ {dir_name}/")
            all_good = False
    
    for dir_name in optional_dirs:
        if Path(dir_name).exists():
            print(f"✓ {dir_name}/")
        else:
            print(f"⚠ {dir_name}/ (will be created)")
    
    return all_good

def check_libtorch():
    """Check LibTorch installation."""
    print("\n🔥 Checking LibTorch...")
    
    libtorch_path = Path("RLGymPPO_CPP/RLGymPPO_CPP/libtorch")
    if libtorch_path.exists():
        print("✓ LibTorch directory found")
        
        # Check for key files
        key_files = [
            "lib/torch.lib",
            "lib/torch_cpu.lib",
            "include/torch/torch.h"
        ]
        
        for file_path in key_files:
            if (libtorch_path / file_path).exists():
                print(f"✓ {file_path}")
            else:
                print(f"❌ {file_path}")
                return False
        
        return True
    else:
        print("❌ LibTorch not found")
        print("Run: python build_project.py")
        return False

def check_collision_meshes():
    """Check collision meshes."""
    print("\n🏎️ Checking collision meshes...")
    
    collision_dir = Path("collision_meshes")
    if not collision_dir.exists():
        print("❌ collision_meshes directory not found")
        return False
    
    files = list(collision_dir.glob("*"))
    mesh_files = [f for f in files if not f.name.startswith("README")]
    
    if not mesh_files:
        print("❌ No collision mesh files found")
        print("Download collision meshes and place them in collision_meshes/")
        return False
    
    print(f"✓ Found {len(mesh_files)} collision mesh files")
    return True

def check_build():
    """Check if project is built."""
    print("\n🔧 Checking build...")
    
    build_dir = Path("RLGymPPO_CPP/build")
    if not build_dir.exists():
        print("❌ Build directory not found")
        print("Run: python build_project.py")
        return False
    
    # Look for executable
    exe_patterns = [
        "RelWithDebInfo/RLGymPPO_CPP_Example.exe",
        "Debug/RLGymPPO_CPP_Example.exe", 
        "Release/RLGymPPO_CPP_Example.exe",
        "RLGymPPO_CPP_Example.exe",
        "RLGymPPO_CPP_Example"
    ]
    
    for pattern in exe_patterns:
        exe_path = build_dir / pattern
        if exe_path.exists():
            print(f"✓ Executable found: {pattern}")
            return True
    
    print("❌ Executable not found")
    print("Run: python build_project.py")
    return False

def main():
    """Main check function."""
    print("RLGymPPO_CPP DirectML Setup Checker")
    print("=" * 50)
    
    checks = [
        ("Python", check_python),
        ("Packages", check_packages),
        ("DirectML", check_directml),
        ("CMake", check_cmake),
        ("Visual Studio", check_visual_studio),
        ("Project Structure", check_project_structure),
        ("LibTorch", check_libtorch),
        ("Collision Meshes", check_collision_meshes),
        ("Build", check_build),
    ]
    
    results = {}
    
    for name, check_func in checks:
        results[name] = check_func()
    
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(checks)
    
    for name, result in results.items():
        status = "✓ PASS" if result else "❌ FAIL"
        print(f"{name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All checks passed! You're ready to train!")
        print("\nNext steps:")
        print("1. Run: python start_training.py")
        print("2. Run: python start_visualization.py (optional)")
    else:
        print(f"\n⚠ {total - passed} issues found. Please fix them before training.")
        
        if not results["Collision Meshes"]:
            print("\n💡 Tip: Collision meshes are required for training.")
            print("   Download them from the RocketSim or RLGym-PPO repositories.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
