#!/usr/bin/env python3
"""
RLGymPPO_CPP Training Script with DirectML Support
This script starts the training process with DirectML acceleration and custom rewards.
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path
import argparse

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    import torch
    import torch_directml
    print(f"✓ PyTorch {torch.__version__} with DirectML support loaded")
    
    # Set DirectML device
    if torch_directml.is_available():
        device = torch_directml.device()
        print(f"✓ DirectML device available: {device}")
    else:
        print("⚠ DirectML not available, falling back to CPU")
        device = torch.device("cpu")
        
except ImportError as e:
    print(f"Error importing DirectML: {e}")
    print("Please run setup_environment.py first")
    sys.exit(1)

from custom_rewards import BALANCED_REWARDS, AGGRESSIVE_REWARDS, DEFENSIVE_REWARDS

def create_training_config(config_name="default", reward_type="balanced"):
    """Create a training configuration file."""
    
    # Select reward function
    reward_functions = {
        "balanced": BALANCED_REWARDS,
        "aggressive": AGGRESSIVE_REWARDS,
        "defensive": DEFENSIVE_REWARDS
    }
    
    reward_func = reward_functions.get(reward_type, BALANCED_REWARDS)
    
    config = {
        "training": {
            "device": "directml" if torch_directml.is_available() else "cpu",
            "batch_size": 50000,  # Optimized for DirectML
            "mini_batch_size": 25000,
            "learning_rate": 2e-4,
            "gamma": 0.99,
            "gae_lambda": 0.95,
            "clip_range": 0.2,
            "entropy_coef": 0.01,
            "value_loss_coef": 0.5,
            "max_grad_norm": 0.5,
            "epochs_per_iteration": 10,
            "target_kl": 0.01,
            "save_every": 10,
            "log_every": 1
        },
        "environment": {
            "num_envs": 16,  # Adjust based on your system
            "steps_per_iteration": 3125,  # 50000 / 16
            "max_episode_length": 300,
            "reward_type": reward_type,
            "team_size": 1,
            "spawn_opponents": True,
            "auto_detect_obs_space": True
        },
        "model": {
            "policy_layers": [512, 512, 512],
            "value_layers": [512, 512, 512],
            "activation": "relu",
            "use_lstm": False,
            "lstm_layers": 1,
            "lstm_size": 512
        },
        "logging": {
            "use_wandb": True,
            "wandb_project": "rlgym_ppo_cpp",
            "wandb_entity": None,
            "log_dir": "./logs",
            "save_dir": "./models"
        }
    }
    
    # Create config directory
    config_dir = Path("configs")
    config_dir.mkdir(exist_ok=True)
    
    # Save config
    config_file = config_dir / f"{config_name}.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✓ Created config: {config_file}")
    return config_file

def check_requirements():
    """Check if all requirements are met."""
    
    # Check if C++ executable exists
    cpp_exe = Path("RLGymPPO_CPP/build/Release/RLGymPPO_CPP.exe")
    if not cpp_exe.exists():
        print("❌ C++ executable not found. Please build the project first.")
        print("Run: cmake --build RLGymPPO_CPP/build --config Release")
        return False
    
    # Check collision meshes
    collision_dir = Path("collision_meshes")
    if not collision_dir.exists() or not any(collision_dir.iterdir()):
        print("❌ collision_meshes directory is empty.")
        print("Please add your collision mesh files to the collision_meshes/ directory")
        return False
    
    # Check DirectML
    if not torch_directml.is_available():
        print("⚠ DirectML not available. Training will use CPU (slower).")
    
    return True

def start_visualization():
    """Start RocketSimVis in a separate process."""
    print("Starting RocketSimVis...")
    
    vis_dir = Path("RocketSimVis")
    if not vis_dir.exists():
        print("❌ RocketSimVis directory not found")
        return None
    
    try:
        # Start the visualization
        vis_process = subprocess.Popen(
            [sys.executable, "src/main.py"],
            cwd=vis_dir,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        print(f"✓ RocketSimVis started (PID: {vis_process.pid})")
        return vis_process
    except Exception as e:
        print(f"❌ Failed to start RocketSimVis: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Start RLGymPPO_CPP training")
    parser.add_argument("--config", default="default", help="Config name")
    parser.add_argument("--reward", choices=["balanced", "aggressive", "defensive"], 
                       default="balanced", help="Reward function type")
    parser.add_argument("--no-vis", action="store_true", help="Don't start visualization")
    parser.add_argument("--resume", help="Resume from checkpoint")
    
    args = parser.parse_args()
    
    print("RLGymPPO_CPP Training Starter")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements not met. Please fix the issues above.")
        return 1
    
    # Create training config
    config_file = create_training_config(args.config, args.reward)
    
    # Start visualization if requested
    vis_process = None
    if not args.no_vis:
        vis_process = start_visualization()
        time.sleep(2)  # Give visualization time to start
    
    # Prepare training command
    cpp_exe = Path("RLGymPPO_CPP/build/Release/RLGymPPO_CPP.exe")
    cmd = [str(cpp_exe), str(config_file)]
    
    if args.resume:
        cmd.extend(["--resume", args.resume])
    
    print(f"\nStarting training with config: {config_file}")
    print(f"Command: {' '.join(cmd)}")
    print("\n" + "=" * 40)
    
    try:
        # Start training
        result = subprocess.run(cmd, cwd=Path.cwd())
        return result.returncode
    except KeyboardInterrupt:
        print("\n\nTraining interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ Error starting training: {e}")
        return 1
    finally:
        # Clean up visualization process
        if vis_process:
            try:
                vis_process.terminate()
                print("✓ Visualization process terminated")
            except:
                pass

if __name__ == "__main__":
    sys.exit(main())
