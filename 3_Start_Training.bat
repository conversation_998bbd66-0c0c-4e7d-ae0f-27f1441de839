@echo off
echo ========================================
echo RLGymPPO_CPP Training Starter
echo ========================================
echo.
echo Starting training with DirectML acceleration...
echo.
echo Available reward types:
echo 1. balanced (default)
echo 2. aggressive
echo 3. defensive
echo.
set /p reward_type="Enter reward type (or press Enter for balanced): "

if "%reward_type%"=="" set reward_type=balanced

echo.
echo Starting training with %reward_type% rewards...
echo Press Ctrl+C to stop training.
echo.

python start_training.py --reward %reward_type%

echo.
echo ========================================
echo Training session ended.
echo ========================================
pause
