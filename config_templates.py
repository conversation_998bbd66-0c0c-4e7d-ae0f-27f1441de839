"""
Configuration Templates for RLGymPPO_CPP
This module contains various pre-configured training setups.
"""

import json
from pathlib import Path

def create_config_templates():
    """Create all configuration templates."""
    
    configs_dir = Path("configs")
    configs_dir.mkdir(exist_ok=True)
    
    # Base configuration
    base_config = {
        "training": {
            "device": "directml",
            "batch_size": 50000,
            "mini_batch_size": 25000,
            "learning_rate": 3e-4,
            "gamma": 0.99,
            "gae_lambda": 0.95,
            "clip_range": 0.2,
            "entropy_coef": 0.01,
            "value_loss_coef": 0.5,
            "max_grad_norm": 0.5,
            "epochs_per_iteration": 10,
            "target_kl": 0.01,
            "save_every": 10,
            "log_every": 1
        },
        "environment": {
            "num_envs": 16,
            "steps_per_iteration": 3125,
            "max_episode_length": 300,
            "team_size": 1,
            "spawn_opponents": True,
            "auto_detect_obs_space": True
        },
        "model": {
            "policy_layers": [512, 512, 512],
            "value_layers": [512, 512, 512],
            "activation": "relu",
            "use_lstm": False,
            "lstm_layers": 1,
            "lstm_size": 512
        },
        "logging": {
            "use_wandb": True,
            "wandb_project": "rlgym_ppo_cpp",
            "wandb_entity": None,
            "log_dir": "./logs",
            "save_dir": "./models"
        }
    }
    
    # Fast training config (for testing)
    fast_config = base_config.copy()
    fast_config["training"].update({
        "batch_size": 25000,
        "mini_batch_size": 12500,
        "epochs_per_iteration": 5,
        "save_every": 5
    })
    fast_config["environment"].update({
        "num_envs": 8,
        "steps_per_iteration": 3125,
        "max_episode_length": 200
    })
    
    # High performance config (for powerful systems)
    high_perf_config = base_config.copy()
    high_perf_config["training"].update({
        "batch_size": 100000,
        "mini_batch_size": 50000,
        "epochs_per_iteration": 15
    })
    high_perf_config["environment"].update({
        "num_envs": 32,
        "steps_per_iteration": 3125
    })
    high_perf_config["model"].update({
        "policy_layers": [1024, 1024, 1024],
        "value_layers": [1024, 1024, 1024]
    })
    
    # 1v1 config
    onevsone_config = base_config.copy()
    onevsone_config["environment"].update({
        "team_size": 1,
        "spawn_opponents": True,
        "max_episode_length": 300
    })
    
    # 2v2 config
    twovstwo_config = base_config.copy()
    twovstwo_config["environment"].update({
        "team_size": 2,
        "spawn_opponents": True,
        "max_episode_length": 450,
        "num_envs": 12  # Fewer envs due to more players
    })
    twovstwo_config["training"].update({
        "batch_size": 60000,
        "mini_batch_size": 30000
    })
    
    # 3v3 config
    threevthree_config = base_config.copy()
    threevthree_config["environment"].update({
        "team_size": 3,
        "spawn_opponents": True,
        "max_episode_length": 600,
        "num_envs": 8  # Even fewer envs
    })
    threevthree_config["training"].update({
        "batch_size": 48000,
        "mini_batch_size": 24000
    })
    
    # Aerial training config
    aerial_config = base_config.copy()
    aerial_config["environment"].update({
        "max_episode_length": 200,  # Shorter episodes for aerial focus
        "reward_type": "aerial_focused"
    })
    aerial_config["training"].update({
        "learning_rate": 5e-4,  # Higher learning rate for aerial skills
        "entropy_coef": 0.02  # More exploration
    })
    
    # Save all configs
    configs = {
        "default": base_config,
        "fast": fast_config,
        "high_performance": high_perf_config,
        "1v1": onevsone_config,
        "2v2": twovstwo_config,
        "3v3": threevthree_config,
        "aerial": aerial_config
    }
    
    for name, config in configs.items():
        config_file = configs_dir / f"{name}.json"
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"Created config: {config_file}")
    
    # Create a config info file
    info_file = configs_dir / "README.md"
    with open(info_file, 'w') as f:
        f.write("# Training Configurations\n\n")
        f.write("This directory contains pre-configured training setups:\n\n")
        f.write("- **default.json**: Balanced training for general gameplay\n")
        f.write("- **fast.json**: Quick training for testing (lower quality)\n")
        f.write("- **high_performance.json**: For powerful systems (high quality)\n")
        f.write("- **1v1.json**: Optimized for 1v1 gameplay\n")
        f.write("- **2v2.json**: Optimized for 2v2 gameplay\n")
        f.write("- **3v3.json**: Optimized for 3v3 gameplay\n")
        f.write("- **aerial.json**: Focused on aerial skill development\n\n")
        f.write("## Usage\n\n")
        f.write("```bash\n")
        f.write("python start_training.py --config 1v1 --reward aggressive\n")
        f.write("```\n")

if __name__ == "__main__":
    create_config_templates()
    print("✓ All configuration templates created!")
