"""
RocketSimVis Integration for RLGymPPO_CPP
This module handles the integration between the C++ training and RocketSimVis.
"""

import socket
import json
import time
import threading
from typing import Dict, Any, Optional
import numpy as np

class RocketSimVisClient:
    """Client for sending game states to RocketSimVis."""
    
    def __init__(self, host: str = "localhost", port: int = 7777):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.last_send_time = 0
        self.send_interval = 1.0 / 60.0  # 60 FPS max
        
    def connect(self) -> bool:
        """Connect to RocketSimVis."""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.connected = True
            print(f"✓ Connected to RocketSimVis at {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to RocketSimVis: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect from RocketSimVis."""
        if self.socket:
            self.socket.close()
            self.socket = None
        self.connected = False
    
    def send_game_state(self, game_state: Dict[str, Any]) -> bool:
        """Send a game state to RocketSimVis."""
        if not self.connected or not self.socket:
            return False
        
        # Rate limiting
        current_time = time.time()
        if current_time - self.last_send_time < self.send_interval:
            return True
        
        try:
            # Convert game state to JSON
            json_data = json.dumps(game_state).encode('utf-8')
            
            # Send to RocketSimVis
            self.socket.sendto(json_data, (self.host, self.port))
            self.last_send_time = current_time
            return True
            
        except Exception as e:
            print(f"❌ Failed to send game state: {e}")
            return False

def convert_rlgym_state_to_vis(state) -> Dict[str, Any]:
    """Convert RLGym state to RocketSimVis format."""
    
    # Ball data
    ball_data = {
        "position": {
            "x": float(state.ball.position[0]),
            "y": float(state.ball.position[1]),
            "z": float(state.ball.position[2])
        },
        "velocity": {
            "x": float(state.ball.linear_velocity[0]),
            "y": float(state.ball.linear_velocity[1]),
            "z": float(state.ball.linear_velocity[2])
        },
        "angular_velocity": {
            "x": float(state.ball.angular_velocity[0]),
            "y": float(state.ball.angular_velocity[1]),
            "z": float(state.ball.angular_velocity[2])
        }
    }
    
    # Players data
    players_data = []
    for i, player in enumerate(state.players):
        player_data = {
            "id": i,
            "team": int(player.team_num),
            "position": {
                "x": float(player.car_data.position[0]),
                "y": float(player.car_data.position[1]),
                "z": float(player.car_data.position[2])
            },
            "velocity": {
                "x": float(player.car_data.linear_velocity[0]),
                "y": float(player.car_data.linear_velocity[1]),
                "z": float(player.car_data.linear_velocity[2])
            },
            "rotation": {
                "pitch": float(player.car_data.euler_angles[0]),
                "yaw": float(player.car_data.euler_angles[1]),
                "roll": float(player.car_data.euler_angles[2])
            },
            "boost": float(player.boost_amount),
            "on_ground": bool(player.on_ground),
            "has_flip": bool(player.has_flip),
            "is_demoed": bool(player.is_demoed)
        }
        players_data.append(player_data)
    
    # Boost pads data (if available)
    boost_pads = []
    if hasattr(state, 'boost_pads'):
        for i, pad in enumerate(state.boost_pads):
            boost_pad_data = {
                "id": i,
                "is_active": bool(pad.is_active),
                "timer": float(pad.timer) if hasattr(pad, 'timer') else 0.0
            }
            boost_pads.append(boost_pad_data)
    
    # Game info
    game_info = {
        "seconds_elapsed": float(getattr(state, 'seconds_elapsed', 0)),
        "game_speed": 1.0,
        "gravity": {"x": 0, "y": 0, "z": -650},
        "is_overtime": False,
        "is_round_active": True
    }
    
    return {
        "ball": ball_data,
        "players": players_data,
        "boost_pads": boost_pads,
        "game_info": game_info,
        "timestamp": time.time()
    }

class VisualizationManager:
    """Manages the visualization connection and data sending."""
    
    def __init__(self, enabled: bool = True, host: str = "localhost", port: int = 7777):
        self.enabled = enabled
        self.client = RocketSimVisClient(host, port) if enabled else None
        self.thread = None
        self.running = False
        self.state_queue = []
        self.max_queue_size = 10
        
    def start(self) -> bool:
        """Start the visualization manager."""
        if not self.enabled:
            return True
            
        if self.client and self.client.connect():
            self.running = True
            self.thread = threading.Thread(target=self._process_states, daemon=True)
            self.thread.start()
            return True
        return False
    
    def stop(self):
        """Stop the visualization manager."""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1.0)
        if self.client:
            self.client.disconnect()
    
    def send_state(self, state):
        """Queue a state for sending to the visualizer."""
        if not self.enabled or not self.running:
            return
            
        # Convert state to visualization format
        vis_state = convert_rlgym_state_to_vis(state)
        
        # Add to queue (with size limit)
        if len(self.state_queue) >= self.max_queue_size:
            self.state_queue.pop(0)  # Remove oldest
        self.state_queue.append(vis_state)
    
    def _process_states(self):
        """Process queued states in background thread."""
        while self.running:
            if self.state_queue and self.client:
                state = self.state_queue.pop(0)
                self.client.send_game_state(state)
            else:
                time.sleep(0.01)  # Small delay when no states

# Global visualization manager instance
_vis_manager: Optional[VisualizationManager] = None

def initialize_visualization(enabled: bool = True, host: str = "localhost", port: int = 7777) -> bool:
    """Initialize the global visualization manager."""
    global _vis_manager
    
    _vis_manager = VisualizationManager(enabled, host, port)
    return _vis_manager.start()

def send_state_to_visualizer(state):
    """Send a state to the visualizer (if initialized)."""
    global _vis_manager
    
    if _vis_manager:
        _vis_manager.send_state(state)

def cleanup_visualization():
    """Clean up the visualization manager."""
    global _vis_manager
    
    if _vis_manager:
        _vis_manager.stop()
        _vis_manager = None

# Convenience function for easy integration
def setup_visualization_for_training(enabled: bool = True) -> bool:
    """Setup visualization for training with automatic cleanup."""
    if not enabled:
        print("Visualization disabled")
        return True
        
    print("Setting up RocketSimVis integration...")
    
    if initialize_visualization(enabled):
        print("✓ RocketSimVis integration ready")
        
        # Register cleanup on exit
        import atexit
        atexit.register(cleanup_visualization)
        
        return True
    else:
        print("⚠ RocketSimVis not available, continuing without visualization")
        return False
