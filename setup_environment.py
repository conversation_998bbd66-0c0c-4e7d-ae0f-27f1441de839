#!/usr/bin/env python3
"""
RLGymPPO_CPP DirectML Setup Script
This script sets up the complete environment for training Rocket League bots
with DirectML acceleration and RocketSimVis visualization.
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """Run a command and return the result."""
    print(f"Running: {cmd}")
    if isinstance(cmd, str):
        cmd = cmd.split()
    
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error running command: {cmd}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        sys.exit(1)
    return result

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_python_packages():
    """Install required Python packages."""
    print("\n=== Installing Python packages ===")
    
    # Core packages
    packages = [
        "torch-directml",  # DirectML support for PyTorch
        "numpy",
        "wandb",  # For metrics tracking
        "matplotlib",
        "opencv-python",
        "psutil",
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        run_command([sys.executable, "-m", "pip", "install", package])
    
    # RocketSimVis requirements
    print("Installing RocketSimVis requirements...")
    rocketsimvis_req = Path("RocketSimVis/requirements.txt")
    if rocketsimvis_req.exists():
        run_command([sys.executable, "-m", "pip", "install", "-r", str(rocketsimvis_req)])

def download_libtorch():
    """Download and extract LibTorch for DirectML."""
    print("\n=== Setting up LibTorch ===")
    
    libtorch_dir = Path("RLGymPPO_CPP/RLGymPPO_CPP/libtorch")
    if libtorch_dir.exists():
        print("✓ LibTorch already exists")
        return
    
    # For DirectML, we use CPU version of LibTorch
    # DirectML acceleration happens through torch-directml
    libtorch_url = "https://download.pytorch.org/libtorch/cpu/libtorch-win-shared-with-deps-2.1.0%2Bcpu.zip"
    libtorch_zip = "libtorch.zip"
    
    print("Downloading LibTorch (CPU version for DirectML compatibility)...")
    urllib.request.urlretrieve(libtorch_url, libtorch_zip)
    
    print("Extracting LibTorch...")
    with zipfile.ZipFile(libtorch_zip, 'r') as zip_ref:
        zip_ref.extractall("RLGymPPO_CPP/RLGymPPO_CPP/")
    
    os.remove(libtorch_zip)
    print("✓ LibTorch installed")

def setup_collision_meshes():
    """Setup collision meshes directory."""
    print("\n=== Setting up collision meshes ===")
    
    collision_dir = Path("collision_meshes")
    if not collision_dir.exists():
        collision_dir.mkdir()
        print("Created collision_meshes directory")
        print("Note: You'll need to add your collision_meshes files here")
    else:
        print("✓ collision_meshes directory exists")

def create_training_configs():
    """Create training configuration files."""
    print("\n=== Creating training configurations ===")

    configs_dir = Path("configs")
    configs_dir.mkdir(exist_ok=True)

    # Import and create config templates
    try:
        from config_templates import create_config_templates
        create_config_templates()
        print("✓ Configuration templates created")
    except ImportError:
        print("⚠ Could not import config_templates, creating basic configs directory")
        print("✓ Configs directory created")

def main():
    """Main setup function."""
    print("RLGymPPO_CPP DirectML Setup")
    print("=" * 40)
    
    # Check system requirements
    check_python_version()
    
    if platform.system() != "Windows":
        print("Warning: DirectML is primarily designed for Windows")
    
    # Setup steps
    install_python_packages()
    download_libtorch()
    setup_collision_meshes()
    create_training_configs()
    
    print("\n" + "=" * 40)
    print("✓ Setup complete!")
    print("\nNext steps:")
    print("1. Add your collision_meshes files to the collision_meshes/ directory")
    print("2. Build the C++ project using CMake")
    print("3. Run start_training.py to begin training")
    print("4. Run start_visualization.py to start RocketSimVis")

if __name__ == "__main__":
    main()
